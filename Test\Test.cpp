﻿#include <Windows.h>
#include <stdio.h>
//using namespace XsDriver;
typedef ULONG(WINAPI*LoadDriverFunc)(CHAR* Name, CHAR* Path);
typedef BOOL(WINAPI*UnLoadDriverFunc)(CHAR* Name);
typedef ULONG (WINAPI*RaWMemFunc)(DWORD ProcessId, ULONG64 Address, ULONG Size, ULONG64 Buffer, UCHAR Type);
typedef ULONG(WINAPI*RaWMemExFunc)(DWORD ProcessId, ULONG64 Address, ULONG Size, ULONG64 Buffer, UCHAR Type);
typedef ULONG(WINAPI*GetModuleBaseFunc)(DWOR<PERSON> ProcessId, ULONG64 ModuleName, ULONG64 ModuleBase);
typedef ULONG(WINAPI*GetModuleExportFunc)(DWORD ProcessId, ULONG64 ModuleBase, ULONG64 ExportName, ULONG64 ExportAddress);
typedef ULONG(WINAPI*NewMemFunc)(DWORD ProcessId, ULON<PERSON> Size, ULONG<PERSON> Buffer);
typedef ULONG(WINAPI*DelMemFunc)(DWORD ProcessId, ULONG64 Address);
typedef ULONG(WINAPI*ProMemFunc)(DWORD ProcessId, ULONG64 Address, ULONG Size, ULONG NewProtect, ULONG64 OldProtect);
typedef ULONG(WINAPI*GetProcIdFunc)(ULONG64, ULONG64);
typedef ULONG(WINAPI*HideProcFunc)(DWORD ProcessId, UCHAR Mode);
typedef ULONG(WINAPI*KillProcFunc)(DWORD ProcessId);
typedef ULONG(WINAPI*UseObjectCallbackFunc)(DWORD ProcessId, UCHAR Type, UCHAR Mode);
typedef ULONG(WINAPI*DelFileFunc)(ULONG64 FilePath);
typedef ULONG(WINAPI*CreThreadFunc)(DWORD ProcessId, ULONG64 StartAddress, ULONG64 Parameter);
typedef ULONG(WINAPI*InjectDllFunc)(DWORD ProcessId, ULONG64 DllPath);
int main()
{
	// 测试的时候请将UserDll.dll放在同一目录下
	// 请先使用驱动加载工具加载驱动
	// 将路径修改为你的驱动路径
	// 名字修改为你的驱动名字
	CHAR DriverPath[] = "C:\\Users\\<USER>\\Desktop\\KernelDriver.sys";
	CHAR DriverName[] = "KernelDriver";
	HMODULE Module = LoadLibraryA("UserDll.dll");
	if (Module == NULL) return 0;
	LoadDriverFunc LoadDriver = (LoadDriverFunc)GetProcAddress(Module, "LoadDriver");
	UnLoadDriverFunc UnloadDriver = (UnLoadDriverFunc)GetProcAddress(Module, "UnLoadDriver");
	printf("LoadDriver:%d\n", LoadDriver(DriverName, DriverPath));
	ULONG64 ProcessId = 0;
	PCHAR ProcessName = (PCHAR)"steam.exe";
	GetProcIdFunc GetProcId = (GetProcIdFunc)GetProcAddress(Module, "GetProcId");
	ULONG Status = GetProcId((ULONG64)ProcessName, (ULONG64)&ProcessId);
	printf("GetProcId Status:%d, ProcessId:%I64u\n", Status, ProcessId);
	ULONG64 ModuleBase = 0;
	PCHAR ModuleName = (PCHAR)"ntdll.dll";
	GetModuleBaseFunc GetModuleBase = (GetModuleBaseFunc)GetProcAddress(Module, "GetModuleBase");
	Status = GetModuleBase((DWORD)ProcessId, (ULONG64)ModuleName, (ULONG64)&ModuleBase);
	printf("GetModuleBase Status:%d, ModuleBase:%I64u\n", Status, ModuleBase);
	ULONG64 ExportAddress = 0;
	PCHAR ExportName = (PCHAR)"NtOpenFile";
	GetModuleExportFunc GetModuleExport = (GetModuleExportFunc)GetProcAddress(Module, "GetModuleExport");
	Status = GetModuleExport((DWORD)ProcessId, (ULONG64)ModuleBase, (ULONG64)ExportName, (ULONG64)&ExportAddress);
	printf("GetModuleExport Status:%d, ExportAddress:%I64u\n", Status, ExportAddress);
	ULONG Buffer = 0;
	ULONG Size = sizeof(ULONG);
	RaWMemFunc RaWMem = (RaWMemFunc)GetProcAddress(Module, "RaWMem");
	Status = RaWMem((DWORD)ProcessId, ModuleBase, Size, (ULONG64)&Buffer, 0);
	printf("RaWMem Status:%d, Buffer:%I64u\n", Status, Buffer);
	printf("UnloadDriver:%d\n", UnloadDriver(DriverName));
	system("pause");


	/*
	PCHAR ProcessName = (PCHAR)"notepad.exe";
	ULONG64 NewProcessId = 0;
	Status = Xs.GetProcId((ULONG64)ProcessName, (ULONG64)&NewProcessId);
	printf("GetProcId Status:%d, NewProcessId:%I64u\n", Status, NewProcessId);
	Sleep(2000);
	Status = Xs.KillProc((DWORD)NewProcessId);
	printf("KillProc Status:%d\n", Status);
	Sleep(2000);
	Status = Xs.UseObjectCallback(ProcessId, 0, 0);
	printf("UseObjectCallback Status:%d\n", Status);
	Sleep(2000);
	PCHAR FilePath = (PCHAR)"\\??\\C:\\Users\\<USER>\\Desktop\\Kernel.sys";
	Status = Xs.DelFile((ULONG64)FilePath);
	printf("DelFile Status:%d\n", Status);
	Sleep(2000);
	PCHAR DllPath = (PCHAR)"C:\\Users\\<USER>\\Desktop\\TestDll.dll";
	Status = Xs.InjectDll(ProcessId, (ULONG64)DllPath);
	printf("InjectDll Status:%d\n", Status);
	*/
	return 0;

}