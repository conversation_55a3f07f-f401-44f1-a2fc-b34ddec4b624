#include "Export.h"
/*
BOOL Install(CONST CHAR* Name, CONST CHAR* Path) {
	SC_HANDLE schSCManager = NULL;
	SC_HANDLE schService = NULL;
	schSCManager = OpenSCManagerA(NULL, NULL, SC_MANAGER_ALL_ACCESS);
	if (schSCManager == NULL) return FALSE;
	schService = CreateServiceA(schSCManager, Name, Name, SERVICE_ALL_ACCESS, SERVICE_FILE_SYSTEM_DRIVER, SERVICE_AUTO_START, SERVICE_ERROR_NORMAL, Path, NULL, NULL, NULL, NULL, NULL);
	if (schService == NULL) {
		CloseServiceHandle(schSCManager);
		return FALSE;
	}
	CloseServiceHandle(schService);
	CloseServiceHandle(schSCManager);
	return TRUE;
}
BOOL UnInstall(CONST CHAR* Name) {
	SC_HANDLE schSCManager = NULL;
	SC_HANDLE schService = NULL;
	SERVICE_STATUS serviceStatus = { 0 };
	schSCManager = OpenSCManagerA(NULL, NULL, SC_MANAGER_ALL_ACCESS);
	if (schSCManager == NULL) return FALSE;
	schService = OpenServiceA(schSCManager, Name, SERVICE_ALL_ACCESS);
	if (schService == NULL) {
		CloseServiceHandle(schSCManager);
		return FALSE;
	}
	ControlService(schService, SERVICE_CONTROL_STOP, &serviceStatus);
	DeleteService(schService);
	CloseServiceHandle(schService);
	CloseServiceHandle(schSCManager);
	return TRUE;
}
BOOL Init(CONST CHAR* Name) {
	SC_HANDLE schSCManager = NULL;
	SC_HANDLE schService = NULL;
	SERVICE_STATUS serviceStatus = { 0 };
	schSCManager = OpenSCManagerA(NULL, NULL, SC_MANAGER_ALL_ACCESS);
	if (schSCManager == NULL) return FALSE;
	schService = OpenServiceA(schSCManager, Name, SERVICE_ALL_ACCESS);
	if (schService == NULL) {
		CloseServiceHandle(schSCManager);
		return FALSE;
	}
	if (!StartServiceA(schService, 0, NULL)) {
		CloseServiceHandle(schService);
		CloseServiceHandle(schSCManager);
		return FALSE;
	}
	CloseServiceHandle(schService);
	CloseServiceHandle(schSCManager);
	return TRUE;
}
BOOL Exit(CONST CHAR* Name) {
	SC_HANDLE schSCManager = NULL;
	SC_HANDLE schService = NULL;
	SERVICE_STATUS serviceStatus = { 0 };
	schSCManager = OpenSCManagerA(NULL, NULL, SC_MANAGER_ALL_ACCESS);
	if (schSCManager == NULL) return FALSE;
	schService = OpenServiceA(schSCManager, Name, SERVICE_ALL_ACCESS);
	if (schService == NULL) {
		CloseServiceHandle(schSCManager);
		return FALSE;
	}
	if (!ControlService(schService, SERVICE_CONTROL_STOP, &serviceStatus)) {
		CloseServiceHandle(schService);
		CloseServiceHandle(schSCManager);
		return FALSE;
	}
	CloseServiceHandle(schService);
	CloseServiceHandle(schSCManager);
	return TRUE;
}
*/
ULONG WINAPI LoadDriver(CHAR* Name, CHAR* Path) {
	/*
	if (!Install(Name, Path)) return FALSE;
	if (!Init(Name)) {
		UnInstall(Name);
		return FALSE;
	}
	*/
	return Xs.Init();
}
BOOL WINAPI UnLoadDriver(CHAR* Name) {
	/*
	if (Xs.Exit() != Status::SUCCESS)
		return FALSE;
	if (!Exit(Name)) return FALSE;
	*/
	return Xs.Exit();
}
ULONG WINAPI RaWMem(DWORD ProcessId, ULONG64 Address, ULONG Size, ULONG64 Buffer, UCHAR Type)
{
	return Xs.RaWMem(ProcessId, Address, Size, Buffer, Type);
}
ULONG WINAPI RaWMemEx(DWORD ProcessId, ULONG64 Address, ULONG Size, ULONG64 Buffer, UCHAR Type)
{
	return Xs.RaWMemEx(ProcessId, Address, Size, Buffer, Type);

}
ULONG WINAPI GetModuleBase(DWORD ProcessId, ULONG64 ModuleName, ULONG64 Buffer)
{
	return Xs.GetModuleBase(ProcessId, ModuleName, Buffer);
}
ULONG WINAPI GetModuleExport(DWORD ProcessId, ULONG64 ModuleBase, ULONG64 ExportName, ULONG64 Buffer)
{
	return Xs.GetModuleExport(ProcessId, ModuleBase, ExportName, Buffer);
}
ULONG WINAPI NewMem(DWORD ProcessId, ULONG Size, ULONG64 Buffer)
{
	return Xs.NewMem(ProcessId, Size, Buffer);
}
ULONG WINAPI DelMem(DWORD ProcessId, ULONG64 Address)
{
	return Xs.DelMem(ProcessId, Address);
}
ULONG WINAPI ProMem(DWORD ProcessId, ULONG64 Address, ULONG Size, ULONG NewProtect, ULONG64 OldProtect)
{
	return Xs.ProMem(ProcessId, Address, Size, NewProtect, OldProtect);
}
ULONG WINAPI GetProcId(ULONG64 ProcessName, ULONG64 ProcessId)
{
	return Xs.GetProcId(ProcessName, ProcessId);
}
ULONG WINAPI HideProc(DWORD ProcessId, UCHAR Mode)
{
	return Xs.HideProc(ProcessId, Mode);
}
ULONG WINAPI KillProc(DWORD ProcessId)
{
	return Xs.KillProc(ProcessId);
}
ULONG WINAPI UseObjectCallback(DWORD ProcessId, UCHAR Type, UCHAR Mode)
{
	return Xs.UseObjectCallback(ProcessId, Type, Mode);
}
ULONG WINAPI DelFile(ULONG64 FilePath)
{
	return Xs.DelFile(FilePath);
}
ULONG WINAPI CreThread(DWORD ProcessId, ULONG64 StartAddress, ULONG64 Parameter)
{
	return Xs.CreateThread(ProcessId, StartAddress, Parameter);
}
ULONG WINAPI InjectDll(DWORD ProcessId, ULONG64 DllPath)
{
	return Xs.InjectDll(ProcessId, DllPath);
}


