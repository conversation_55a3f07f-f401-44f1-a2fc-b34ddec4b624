#include "Stdafx.h"

UNICODE_STRING LinkName = { 0 };
UNICODE_STRING DeviceName = { 0 };
PDEVICE_OBJECT DeviceObject = NULL;

// 驱动服务入口
NTSTATUS DriverInit(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath) 
{
	UNREFERENCED_PARAMETER(RegistryPath);
	NTSTATUS Status = STATUS_SUCCESS;
	DriverObject->MajorFunction[IRP_MJ_CREATE] = DispatchInit;
	DriverObject->MajorFunction[IRP_MJ_CLOSE] = DispatchExit;
	DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL] = DispatchCtrl;
	DriverObject->DriverUnload = DriverExit;
	RtlInitUnicodeString(&DeviceName, DEVICE_NAME);
	Status = IoCreateDevice(DriverObject, 0, &DeviceName, FILE_DEVICE_UNKNOWN, 0, FALSE, &DeviceObject);
	if (!NT_SUCCESS(Status))
		return Status;
	RtlInitUnicodeString(&LinkName, DEVICE_LINK_NAME);
	Status = IoCreateSymbolicLink(&LinkName, &DeviceName);
	if (!NT_SUCCESS(Status))
	{
		IoDeleteDevice(DeviceObject);
		return Status;
	}
	// 如果驱动是正常有效签名 请注释掉下面两行代码
	PLDR_DATA_TABLE_ENTRY64 LdrEntry = (PLDR_DATA_TABLE_ENTRY64)DriverObject->DriverSection;
	LdrEntry->Flags |= 0x20;
	// 注册句柄回调函数
	if (RegisterObjectCallBack() != SUCCESS) 
	{
		IoDeleteSymbolicLink(&LinkName);
		IoDeleteDevice(DeviceObject);
		return STATUS_UNSUCCESSFUL;
	}
	return Status;
}
// 驱动服务退出
VOID DriverExit(PDRIVER_OBJECT DriverObject)
{	
	UNREFERENCED_PARAMETER(DriverObject);
	// 注销句柄回调函数
	if (UnRegisterObjectCallBack() != SUCCESS)
		return;
	IoDeleteSymbolicLink(&LinkName);
	IoDeleteDevice(DeviceObject);
}
// 驱动服务创建
NTSTATUS DispatchInit(PDEVICE_OBJECT DriverObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DriverObject);
	Irp->IoStatus.Status = STATUS_SUCCESS;
	Irp->IoStatus.Information = 0;
	IoCompleteRequest(Irp, IO_NO_INCREMENT);
	return STATUS_SUCCESS;
}
// 驱动服务销毁
NTSTATUS DispatchExit(PDEVICE_OBJECT DriverObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DriverObject);
	Irp->IoStatus.Status = STATUS_SUCCESS;
	Irp->IoStatus.Information = 0;
	IoCompleteRequest(Irp, IO_NO_INCREMENT);
	return STATUS_SUCCESS;
}
// 驱动服务控制
NTSTATUS DispatchCtrl(PDEVICE_OBJECT DriverObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DriverObject);
	NTSTATUS Status = STATUS_SUCCESS;
	PIO_STACK_LOCATION IoStackLocation = NULL;
	PVOID InputData = NULL;
	PVOID OutputData = NULL;
	ULONG InputDataLength = 0;
	ULONG OutputDataLength = 0;
	ULONG IoControlCode = 0;
	IoStackLocation = IoGetCurrentIrpStackLocation(Irp);
	IoControlCode = IoStackLocation->Parameters.DeviceIoControl.IoControlCode;
	InputData = Irp->AssociatedIrp.SystemBuffer;
	OutputData = Irp->AssociatedIrp.SystemBuffer;
	InputDataLength = IoStackLocation->Parameters.DeviceIoControl.InputBufferLength;
	OutputDataLength = IoStackLocation->Parameters.DeviceIoControl.OutputBufferLength;
	switch (IoControlCode)
	{
	case IOCTL_IO_RAWMEM:
	{
		// 读写内存
		// 初始化需要的参数

		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;       // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID Address = (PVOID)Data->Address;                // 这个地址是直接传递过来的 所以我们不需要转换
		PVOID Buffer = (PVOID)Data->Buffer;                  // 这个地址是直接传递过来的 所以我们不需要转换
		ULONG Size = *(PULONG)&Data->Size;                   // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		UCHAR Type = *(PUCHAR)&Data->Type;                   // 这个是一个1字节的值 所以我们要将这个值转换为指针再读取
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = RaWMem(ProcessId, Address, Size, Buffer, Type);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;
	}
	case IOCTL_IO_RAWMEMEX:
	{
		// 读写内存
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;       // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID Address = (PVOID)Data->Address;                // 这个地址是直接传递过来的 所以我们不需要转换
		ULONG Size = *(PULONG)&Data->Size;                   // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID Buffer = (PVOID)Data->Buffer;                  // 这个地址是直接传递过来的 所以我们不需要转换
		UCHAR Type = *(PUCHAR)&Data->Type;                   // 这个是一个1字节的值 所以我们要将这个值转换为指针再读取
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = RaWMemEx(ProcessId, Address, Size, Buffer, Type);
		// 如果是读操作 则拷贝数据到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;
	}
	case IOCTL_IO_GETMODBASE:
	{
		// 获取模块基址
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;       // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PCHAR Name = (PCHAR)Data->Address;                  // 这个地址是直接传递过来的 所以我们不需要转换
		PVOID Buffer = (PVOID)Data->Buffer;                  // 这个地址是直接传递过来的 所以我们不需要转换
		// 转换为UNICODE_STRING
		ANSI_STRING AnsiBuffer = { 0 };
		UNICODE_STRING ModuleName = { 0 };
		AnsiBuffer.Buffer = Name;//文本型指针
		AnsiBuffer.Length = AnsiBuffer.MaximumLength = (USHORT)strlen(Name);//扫到/0位置
		RtlAnsiStringToUnicodeString(&ModuleName, &AnsiBuffer, TRUE);
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = GetModuleBase(ProcessId, ModuleName, Buffer);
		RtlFreeUnicodeString(&ModuleName);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;
	}
	case IOCTL_IO_GETMODEXPORT:
	{
		// 获取模块导出函数地址
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;       // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID ModuleBase = (PVOID)Data->Address;             // 这个地址是直接传递过来的 所以我们不需要转换
		PCHAR Name = (PCHAR)Data->Size;                   // 这个地址是直接传递过来的 所以我们不需要转换
		PVOID Buffer = (PVOID)Data->Buffer;                  // 这个地址是直接传递过来的 所以我们不需要转换
		SIZE_T Size = strlen(Name);
		if (Size > 0x1000)
		{
			Status = STATUS_UNSUCCESSFUL;
			break;
		}
		CHAR FunctionName[0x1000] = { 0 };
		RtlCopyMemory(FunctionName, Name, Size);
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = GetModuleExport(ProcessId, ModuleBase, FunctionName, Buffer);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;


	}
	case IOCTL_IO_NEWMEM:
	{
		// 申请内存
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;       // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		ULONG Size = *(PULONG)&Data->Size;                   // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID Buffer = (PVOID)Data->Buffer;                  // 这个地址是直接传递过来的 所以我们不需要转换
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = NewMem(ProcessId, Size, Buffer);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;
	}
	case IOCTL_IO_DELMEM:
	{
		// 释放内存
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;        // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID Address = (PVOID)Data->Address;                // 这个地址是直接传递过来的 所以我们不需要转换
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = DelMem(ProcessId, Address);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;
	}
	case IOCTL_IO_PROMEM:
	{
		// 修改内存属性
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;       // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID Address = (PVOID)Data->Address;                // 这个地址是直接传递过来的 所以我们不需要转换
		ULONG Size = *(PULONG)&Data->Size;					 // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		ULONG NewProtect = *(PULONG)&Data->Type;			 // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID Buffer = (PVOID)Data->Buffer;			 // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = ProMem(ProcessId, Address, Size, NewProtect, Buffer);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;

	}
	case IOCTL_IO_GETPROCID:
	{
		// 获取进程ID
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		PCHAR Name = (PCHAR)Data->ProcessId;                  // 这个地址是直接传递过来的 所以我们不需要转换
		PVOID Buffer = (PVOID)Data->Buffer;                   // 这个地址是直接传递过来的 所以我们不需要转换
		// 转换为UNICODE_STRING
		ANSI_STRING AnsiBuffer = { 0 };
		UNICODE_STRING ProcessName = { 0 };
		AnsiBuffer.Buffer = Name;//文本型指针
		AnsiBuffer.Length = AnsiBuffer.MaximumLength = (USHORT)strlen(Name);//扫到/0位置
		RtlAnsiStringToUnicodeString(&ProcessName, &AnsiBuffer, TRUE);
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = GetProcId(ProcessName, Buffer);
		RtlFreeUnicodeString(&ProcessName);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));

		Status = STATUS_SUCCESS;
		break;
	}
	case IOCTL_IO_HIDEPROC:
	{
		// 隐藏进程
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;         // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		UCHAR Type = *(PUCHAR)&Data->Type;                     // 这个是一个1字节的值 所以我们要将这个值转换为指针再读取
		USHORT Ret = { 0 };
		// 调用函数
		Ret = HideProc(ProcessId, Type);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;

	}
	case IOCTL_IO_KILLPROC:
	{
		// 结束进程
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;         // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		USHORT Ret = { 0 };
		// 调用函数
		Ret = KillProc(ProcessId);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;
	}
	case IOCTL_IO_USEOBCALLBACK:
	{
		// 使用句柄回调函数
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;         // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		UCHAR Type = *(PUCHAR)&Data->Address;                   // 这个是一个2字节的值 所以我们要将这个值转换为指针再读取
		UCHAR Mode = *(PUCHAR)&Data->Size;                   // 这个是一个2字节的值 所以我们要将这个值转换为指针再读取
		USHORT Ret = { 0 };
		// 调用函数
		Ret = UseObjectCallBack(ProcessId, Type, Mode);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;

	}
	case IOCTL_IO_DELFILE:
	{
		// 删除文件
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		PCHAR Path = (PCHAR)Data->ProcessId;                  // 这个地址是直接传递过来的 所以我们不需要转换
		ANSI_STRING AnsiBuffer = { 0 };
		UNICODE_STRING FilePath = { 0 };
		AnsiBuffer.Buffer = Path;//文本型指针
		AnsiBuffer.Length = AnsiBuffer.MaximumLength = (USHORT)strlen(Path);//扫到/0位置
		RtlAnsiStringToUnicodeString(&FilePath, &AnsiBuffer, TRUE);
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = DelFile(FilePath);
		RtlFreeUnicodeString(&FilePath);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;
	}
	case IOCTL_IO_CREATETHREAD:
	{
		// 创建线程
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;         // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PVOID StartAddress = (PVOID)Data->Address;            // 这个地址是直接传递过来的 所以我们不需要转换
		PVOID ParmaAddress = (PVOID)Data->Size;               // 这个地址是直接传递过来的 所以我们不需要转换
		USHORT Ret = { 0 };
		// 调用函数
		Ret = CreateThread(ProcessId, StartAddress, ParmaAddress);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;

	}
	case IOCTL_IO_INJECTDLL:
	{
		// 注入DLL
		// 初始化需要的参数
		PRWMEMDATA Data = (PRWMEMDATA)InputData;
		HANDLE ProcessId = *(PHANDLE)&Data->ProcessId;         // 这个是一个4字节的值 所以我们要将这个值转换为指针再读取
		PCHAR DllPath = (PCHAR)Data->Address;                 // 这个地址是直接传递过来的 所以我们不需要转换
		USHORT Ret = FAILURE;
		// 调用函数
		Ret = InjectDll(ProcessId, DllPath);
		// 将结果拷贝到输出缓冲区
		RtlCopyMemory(OutputData, &Ret, sizeof(USHORT));
		Status = STATUS_SUCCESS;
		break;

	}
	default:
		Status = STATUS_UNSUCCESSFUL;
		break;
	}
	if (Status == STATUS_SUCCESS)
	{
		Irp->IoStatus.Information = OutputDataLength;
	}
	else
	{
		Irp->IoStatus.Information = 0;
	}
	Irp->IoStatus.Status = Status;
	IoCompleteRequest(Irp, IO_NO_INCREMENT);
	return Status;
}