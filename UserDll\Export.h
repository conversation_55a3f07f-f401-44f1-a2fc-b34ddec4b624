#pragma once



#include "ToKernel.h"

#include <io.h>







using namespace XsDriver;

#ifdef __cplusplus 
#define EXPORT extern "C" __declspec (dllexport)
#else
#define EXPORT __declspec (dllexport)
#endif


EXPORT ULONG WINAPI LoadDriver(CHAR* Name, CHAR* Path);
EXPORT BOOL WINAPI UnLoadDriver(CHAR* Name);
EXPORT ULONG WINAPI RaWMem(DWORD ProcessId, ULONG64 Address, ULONG Size, ULONG64 Buffer, UCHAR Type);
EXPORT ULONG WINAPI RaWMemEx(DWORD ProcessId, ULONG64 Address, ULON<PERSON> Size, ULONG<PERSON> Buffer, UCHAR Type);
EXPORT ULONG WINAPI GetModuleBase(DWORD ProcessId, ULONG64 ModuleName, ULONG64 Buffer);
EXPORT ULONG WINAPI GetModuleExport(DWORD ProcessId, ULONG<PERSON> ModuleBase, ULONG64 ExportName, ULONG64 Buffer);
EXPORT ULONG WINAPI NewMem(DWORD ProcessId, ULONG Size, ULONG64 Buffer);
EXPORT ULONG WINAPI DelMem(DWORD ProcessId, ULONG64 Address);
EXPORT ULONG WINAPI ProMem(DWORD ProcessId, ULONG64 Address, ULONG Size, ULONG NewProtect, ULONG64 OldProtect);
EXPORT ULONG WINAPI GetProcId(ULONG64 ProcessName, ULONG64 ProcessId);
EXPORT ULONG WINAPI HideProc(DWORD ProcessId, UCHAR Mode);
EXPORT ULONG WINAPI KillProc(DWORD ProcessId);
EXPORT ULONG WINAPI UseObjectCallback(DWORD ProcessId, UCHAR Type, UCHAR Mode);
EXPORT ULONG WINAPI DelFile(ULONG64 FilePath);
EXPORT ULONG WINAPI CreThread(DWORD ProcessId, ULONG64 StartAddress, ULONG64 Parameter);
EXPORT ULONG WINAPI InjectDll(DWORD ProcessId, ULONG64 DllPath);



